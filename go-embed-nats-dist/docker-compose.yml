services:
  app1:
    build: .
    environment:
      - CLUSTER_NAME=docker-cluster
      - JETSTREAM_STORE_DIR=/data/jetstream
      - DEBUG=true
      - DISCOVERY_METHOD=auto
    networks:
      - nats-cluster
    ports:
      - "8080:8080"  # HTTP API
      - "4222:4222"  # NATS client
      - "6222:6222"  # NATS cluster
    volumes:
      - ./data:/data
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:8080/health"]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 30s

  app2:
    build: .
    environment:
      - CLUSTER_NAME=docker-cluster
      - JETSTREAM_STORE_DIR=/data/jetstream
      - DEBUG=true
      - DISCOVERY_METHOD=auto
    networks:
      - nats-cluster
    ports:
      - "8081:8080"  # HTTP API
      - "4223:4222"  # NATS client
      - "6223:6222"  # NATS cluster
    volumes:
      - ./data:/data
    healthcheck:
      test: ["<PERSON><PERSON>", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:8080/health"]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 30s

  app3:
    build: .
    environment:
      - CLUSTER_NAME=docker-cluster
      - JETSTREAM_STORE_DIR=/data/jetstream
      - DEBUG=true
      - DISCOVERY_METHOD=auto
    networks:
      - nats-cluster
    ports:
      - "8082:8080"  # HTTP API
      - "4224:4222"  # NATS client
      - "6224:6222"  # NATS cluster
    volumes:
      - ./data:/data
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:8080/health"]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 30s

networks:
  nats-cluster:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  data:
