# Go Embedded NATS with Auto-Clustering

A high-performance Go application that embeds NATS server with JetStream, implements broadcast-based auto-discovery for cluster formation, and uses Watermill for pub/sub messaging.

## Features

- **Embedded NATS Server**: NATS server with JetStream embedded directly in the Go application
- **Auto-Discovery**: UDP broadcast-based cluster discovery (no hardcoded IPs or DNS SRV)
- **Watermill Integration**: High-performance pub/sub using Watermill with NATS JetStream backend
- **Docker Compose Support**: Easy local testing with multiple replicas
- **Kubernetes Ready**: Designed for horizontal autoscaling in Kubernetes
- **REST API**: HTTP endpoints for sending messages and monitoring cluster health

## Quick Start

### Local Development

1. **Build and run with Docker Compose:**
   ```bash
   docker-compose up --build --scale app=3
   ```

2. **Test message broadcasting:**
   ```bash
   ./scripts/test-cluster.sh
   ```

3. **Send a broadcast message manually:**
   ```bash
   curl -X POST "http://localhost:8080/api/v1/broadcast" \
     -H "Content-Type: application/json" \
     -d '{"topic": "broadcast.messages", "message": "Hello from node 1!"}'
   ```

4. **Check cluster status:**
   ```bash
   curl http://localhost:8080/cluster
   ```

### Scaling the Cluster

```bash
# Scale to 5 nodes
docker-compose up --scale app=5 --no-recreate

# Scale down to 2 nodes
docker-compose up --scale app=2 --no-recreate
```

## API Endpoints

- `GET /` - Application info
- `GET /health` - Health check with cluster info
- `GET /cluster` - Detailed cluster information
- `POST /api/v1/messages` - Send a message to a specific topic
- `POST /api/v1/broadcast` - Send a broadcast message to all nodes

## Configuration

The application is configured via environment variables:

| Variable | Default | Description |
|----------|---------|-------------|
| `CLUSTER_NAME` | `auto-cluster` | NATS cluster name |
| `DISCOVERY_METHOD` | `auto` | Discovery method: `auto`, `static`, `dns`, `docker`, `kubernetes` |
| `NATS_ROUTES` | `""` | Static routes (comma-separated): "nats://host1:6222,nats://host2:6222" |
| `DISCOVERY_SEED_HOSTS` | `""` | DNS seed hosts for discovery (comma-separated) |
| `DOCKER_SERVICE_NAMES` | `""` | Docker service names to discover (comma-separated) |
| `COMPOSE_PROJECT_NAME` | `""` | Docker Compose project name (auto-detected) |
| `KUBERNETES_NAMESPACE` | `default` | Kubernetes namespace for service discovery |
| `KUBERNETES_SERVICE_NAME` | `nats` | Kubernetes service name for discovery |
| `NATS_CLIENT_PORT` | `4222` | NATS client port |
| `NATS_CLUSTER_PORT` | `6222` | NATS cluster port |
| `HTTP_PORT` | `8080` | HTTP API port |
| `DEBUG` | `false` | Enable debug logging |

## Architecture

### Components

1. **Embedded NATS Server**: Runs NATS with JetStream in the same process
2. **Cluster Manager**: Simple manager that uses NATS native clustering
3. **Watermill Manager**: Handles pub/sub messaging with performance optimizations
4. **HTTP API**: REST endpoints for interaction and monitoring

### Discovery Methods

The application supports multiple discovery methods:

1. **Auto Discovery** (`DISCOVERY_METHOD=auto`): Tries multiple methods automatically
   - Static routes (if `NATS_ROUTES` is set)
   - DNS seed hosts (if `DISCOVERY_SEED_HOSTS` is set)
   - Docker Compose service discovery
   - Kubernetes service discovery

2. **Static Routes** (`DISCOVERY_METHOD=static`): Use predefined routes
   ```bash
   NATS_ROUTES=nats://host1:6222,nats://host2:6222
   ```

3. **DNS Discovery** (`DISCOVERY_METHOD=dns`): Use DNS to find seed hosts
   ```bash
   DISCOVERY_SEED_HOSTS=nats-seed.example.com,nats-backup.example.com
   ```

4. **Docker Discovery** (`DISCOVERY_METHOD=docker`): Auto-discover Docker services
   - **Option A**: Specify service names: `DOCKER_SERVICE_NAMES=app1,app2,app3`
   - **Option B**: Use project name: `COMPOSE_PROJECT_NAME=myproject` (auto-detects appX services)
   - **Option C**: Network scanning: Scans Docker network for NATS cluster ports

5. **Kubernetes Discovery** (`DISCOVERY_METHOD=kubernetes`): Use K8s service discovery
   ```bash
   KUBERNETES_NAMESPACE=production
   KUBERNETES_SERVICE_NAME=nats-cluster
   ```

### Message Flow

1. Application starts and discovers NATS cluster members
2. NATS servers connect to discovered routes on startup
3. NATS automatically discovers all cluster members via gossip protocol
4. Messages sent via Watermill are distributed across the cluster
5. All nodes receive broadcast messages regardless of which node sent them

## Testing

### Verify Cluster Formation

```bash
# Check logs for cluster formation
docker-compose logs app | grep -E "(Discovered new peer|cluster)"

# Check cluster status via API
curl -s http://localhost:8080/cluster | jq .
```

### Test Message Broadcasting

```bash
# Send broadcast message
curl -X POST "http://localhost:8080/api/v1/broadcast" \
  -H "Content-Type: application/json" \
  -d '{"message": "Test broadcast"}'

# Check all container logs to verify all nodes received the message
docker-compose logs app | grep "Received broadcast message"
```

### Performance Testing

```bash
# Send multiple messages quickly
for i in {1..10}; do
  curl -X POST "http://localhost:8080/api/v1/broadcast" \
    -H "Content-Type: application/json" \
    -d "{\"message\": \"Message $i\"}" &
done
wait
```

## Kubernetes Deployment

The application is designed to work in Kubernetes with horizontal pod autoscaling:

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: go-embed-nats-dist
spec:
  replicas: 3
  selector:
    matchLabels:
      app: go-embed-nats-dist
  template:
    metadata:
      labels:
        app: go-embed-nats-dist
    spec:
      containers:
      - name: app
        image: go-embed-nats-dist:latest
        env:
        - name: DISCOVERY_METHOD
          value: "broadcast"
        - name: CLUSTER_NAME
          value: "k8s-cluster"
        ports:
        - containerPort: 8080
        - containerPort: 4222
        - containerPort: 6222
        - containerPort: 7222
          protocol: UDP
```

## Troubleshooting

### Common Issues

1. **Cluster not forming**: Check UDP port 7222 is not blocked by firewall
2. **Messages not broadcasting**: Verify JetStream is enabled and working
3. **High memory usage**: Adjust JetStream memory limits via environment variables

### Debug Mode

Enable debug logging:
```bash
docker-compose up --build --scale app=3 -e DEBUG=true
```

### Logs

```bash
# View all logs
docker-compose logs -f app

# View logs for specific service
docker-compose logs -f app_1

# Filter for cluster-related logs
docker-compose logs app | grep -E "(cluster|peer|broadcast)"
```
