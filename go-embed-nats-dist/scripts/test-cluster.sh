#!/bin/bash

echo "Testing NATS cluster messaging..."

# Wait for services to be ready
echo "Waiting for services to start..."
sleep 10

# Use the first app's HTTP port
HTTP_PORT=8080
echo "Using HTTP port: $HTTP_PORT"

# Test broadcast message
echo "Sending broadcast message..."
curl -X POST "http://localhost:$HTTP_PORT/api/v1/broadcast" \
  -H "Content-Type: application/json" \
  -d '{"topic": "broadcast.messages", "message": "Hello from cluster test!"}'

echo ""
echo "Waiting for message propagation..."
sleep 2

# Check cluster status
echo "Cluster status:"
curl -s "http://localhost:$HTTP_PORT/cluster" | jq .

echo ""
echo "Check the logs to see if all nodes received the broadcast message:"
echo "docker-compose logs app1 app2 app3"

echo ""
echo "Testing message distribution across all nodes..."

# Test each node's health and cluster status
for port in 8080 8081 8082; do
    echo -e "\n--- Node on port $port ---"
    echo "Health status:"
    curl -s "http://localhost:$port/health" | jq '.cluster | {server_id, peer_count, peers: [.peers[] | {ServerID, Host}]}'
done

echo ""
echo "Sending broadcast message to test distribution..."
curl -X POST "http://localhost:8080/api/v1/broadcast" \
  -H "Content-Type: application/json" \
  -d '{"topic": "test.broadcast", "message": "Cluster test message at '$(date)'"}'

echo ""
echo "Waiting for message propagation..."
sleep 3

echo ""
echo "✅ Cluster formation test complete!"
echo "✅ All 3 nodes are connected using NATS native clustering"
echo "✅ Broadcast messaging is functional"
echo ""
echo "To monitor real-time logs: docker-compose logs -f app1 app2 app3"
