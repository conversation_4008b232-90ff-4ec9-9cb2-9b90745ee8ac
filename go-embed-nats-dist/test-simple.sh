#!/bin/bash

echo "Testing NATS cluster messaging..."

# Wait for services to be ready
echo "Waiting for services to start..."
sleep 10

# Get the first available port for HTTP API
HTTP_PORT=$(docker-compose port app 8080 | cut -d: -f2)
echo "Using HTTP port: $HTTP_PORT"

# Test broadcast message
echo "Sending broadcast message..."
curl -X POST "http://localhost:$HTTP_PORT/api/v1/broadcast" \
  -H "Content-Type: application/json" \
  -d '{"topic": "broadcast.messages", "message": "Hello from cluster test!"}'

echo ""
echo "Waiting for message propagation..."
sleep 2

# Check cluster status
echo "Cluster status:"
curl -s "http://localhost:$HTTP_PORT/cluster" | jq .

echo ""
echo "Check the logs to see if all nodes received the broadcast message:"
echo "docker-compose logs app"
