package api

import (
	"fmt"
	"net/http"
	"time"

	"go-embed-nats-dist/internal/config"
	"go-embed-nats-dist/internal/nats"
	"go-embed-nats-dist/internal/pubsub"

	"github.com/gin-gonic/gin"
)

type Server struct {
	config         *config.Config
	clusterManager *nats.ClusterManager
	pubsub         *pubsub.SimplePubSub
	router         *gin.Engine
}

type MessageRequest struct {
	Topic   string `json:"topic" binding:"required"`
	Message string `json:"message" binding:"required"`
}

type MessageResponse struct {
	Success   bool   `json:"success"`
	MessageID string `json:"message_id,omitempty"`
	Error     string `json:"error,omitempty"`
}

type HealthResponse struct {
	Status    string                 `json:"status"`
	Timestamp string                 `json:"timestamp"`
	Version   string                 `json:"version"`
	Cluster   map[string]interface{} `json:"cluster"`
}

func NewServer(cfg *config.Config, clusterManager *nats.ClusterManager, pubsub *pubsub.SimplePubSub) *Server {
	if !cfg.App.Debug {
		gin.SetMode(gin.ReleaseMode)
	}

	return &Server{
		config:         cfg,
		clusterManager: clusterManager,
		pubsub:         pubsub,
		router:         gin.Default(),
	}
}

func (s *Server) Start() error {
	s.setupRoutes()

	addr := fmt.Sprintf(":%d", s.config.HTTP.Port)
	return s.router.Run(addr)
}

func (s *Server) setupRoutes() {
	// Health check endpoint
	s.router.GET("/health", s.healthHandler)

	// Cluster info endpoint
	s.router.GET("/cluster", s.clusterHandler)

	// Message endpoints
	api := s.router.Group("/api/v1")
	{
		api.POST("/messages", s.sendMessageHandler)
		api.POST("/broadcast", s.broadcastMessageHandler)
	}

	// Static info endpoint
	s.router.GET("/", s.infoHandler)
}

func (s *Server) healthHandler(c *gin.Context) {
	clusterInfo := s.clusterManager.GetClusterInfo()

	response := HealthResponse{
		Status:    "healthy",
		Timestamp: time.Now().Format(time.RFC3339),
		Version:   s.config.App.Version,
		Cluster:   clusterInfo,
	}

	c.JSON(http.StatusOK, response)
}

func (s *Server) clusterHandler(c *gin.Context) {
	clusterInfo := s.clusterManager.GetClusterInfo()
	c.JSON(http.StatusOK, clusterInfo)
}

func (s *Server) sendMessageHandler(c *gin.Context) {
	var req MessageRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, MessageResponse{
			Success: false,
			Error:   err.Error(),
		})
		return
	}

	// Send message via simple pub/sub
	err := s.pubsub.PublishMessage(req.Topic, []byte(req.Message))
	if err != nil {
		c.JSON(http.StatusInternalServerError, MessageResponse{
			Success: false,
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, MessageResponse{
		Success:   true,
		MessageID: fmt.Sprintf("%d", time.Now().UnixNano()),
	})
}

func (s *Server) broadcastMessageHandler(c *gin.Context) {
	var req MessageRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, MessageResponse{
			Success: false,
			Error:   err.Error(),
		})
		return
	}

	// Send broadcast message - this will be received by all nodes
	err := s.pubsub.PublishMessage("broadcast.messages", []byte(req.Message))
	if err != nil {
		c.JSON(http.StatusInternalServerError, MessageResponse{
			Success: false,
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, MessageResponse{
		Success:   true,
		MessageID: fmt.Sprintf("broadcast-%d", time.Now().UnixNano()),
	})
}

func (s *Server) infoHandler(c *gin.Context) {
	clusterInfo := s.clusterManager.GetClusterInfo()

	info := map[string]interface{}{
		"app":     s.config.App.Name,
		"version": s.config.App.Version,
		"cluster": clusterInfo,
		"endpoints": map[string]string{
			"health":    "/health",
			"cluster":   "/cluster",
			"messages":  "POST /api/v1/messages",
			"broadcast": "POST /api/v1/broadcast",
		},
	}

	c.JSON(http.StatusOK, info)
}
