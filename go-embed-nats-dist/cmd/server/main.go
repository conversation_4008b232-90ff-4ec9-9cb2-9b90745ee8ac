package main

import (
	"log"
	"os"
	"os/signal"
	"syscall"

	"go-embed-nats-dist/internal/config"
	"go-embed-nats-dist/internal/nats"
	"go-embed-nats-dist/internal/pubsub"
	"go-embed-nats-dist/pkg/api"
)

func main() {
	log.Println("Starting go-embed-nats-dist application...")

	// Load configuration
	cfg := config.Load()
	log.Printf("Loaded configuration: cluster=%s, routes=%v",
		cfg.NATS.ClusterName, cfg.NATS.Routes)

	// Create embedded NATS server
	natsServer := nats.NewEmbeddedNATS(cfg)

	// Create cluster manager
	clusterManager := nats.NewClusterManager(natsServer)

	// Start NATS server
	if err := natsServer.Start(); err != nil {
		log.Fatalf("Failed to start NATS server: %v", err)
	}
	defer natsServer.Stop()

	// Start cluster manager
	if err := clusterManager.Start(); err != nil {
		log.Fatalf("Failed to start cluster manager: %v", err)
	}
	defer clusterManager.Stop()

	// Create Simple PubSub manager
	simplePubSub := pubsub.NewSimplePubSub(cfg, natsServer.GetClientURL())
	if err := simplePubSub.Start(natsServer.GetClientURL()); err != nil {
		log.Fatalf("Failed to start Simple PubSub manager: %v", err)
	}
	defer simplePubSub.Stop()

	// Create and start HTTP server
	httpServer := api.NewServer(cfg, clusterManager, simplePubSub)

	// Start HTTP server in background
	go func() {
		log.Printf("Starting HTTP server on port %d", cfg.HTTP.Port)
		if err := httpServer.Start(); err != nil {
			log.Fatalf("Failed to start HTTP server: %v", err)
		}
	}()

	log.Printf("Application started successfully!")
	log.Printf("- NATS client: %s", natsServer.GetClientURL())
	log.Printf("- NATS cluster: %s", natsServer.GetClusterURL())
	log.Printf("- HTTP API: http://localhost:%d", cfg.HTTP.Port)
	log.Printf("- Server ID: %s", natsServer.GetServerID())

	// Wait for interrupt signal
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)
	<-sigChan

	log.Println("Shutting down application...")
}
