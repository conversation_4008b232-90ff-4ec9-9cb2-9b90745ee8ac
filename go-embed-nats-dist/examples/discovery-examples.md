# Dynamic Discovery Examples

This document shows how to achieve dynamic NATS cluster discovery without hardcoding routes.

## 1. Docker Compose with Zero-Config Auto-Discovery

```yaml
# docker-compose.yml - No hardcoded service names!
services:
  app1:
    build: .
    environment:
      - DISCOVERY_METHOD=auto  # Scans network automatically
      - CLUSTER_NAME=docker-cluster
    networks:
      - nats-cluster

  app2:
    build: .
    environment:
      - DISCOVERY_METHOD=auto  # Finds app1 via network scan
      - CLUSTER_NAME=docker-cluster
    networks:
      - nats-cluster

  app3:
    build: .
    environment:
      - DISCOVERY_METHOD=auto  # Finds app1 & app2 via network scan
      - CLUSTER_NAME=docker-cluster
    networks:
      - nats-cluster

networks:
  nats-cluster:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16  # Enables network scanning
```

## 1b. Docker Compose with Service Names (Optional)

```yaml
# If you want to specify service names explicitly
services:
  app1:
    build: .
    environment:
      - DISCOVERY_METHOD=docker
      - DOCKER_SERVICE_NAMES=app2,app3  # Only specify others
      - CLUSTER_NAME=docker-cluster

  app2:
    build: .
    environment:
      - DISCOVERY_METHOD=docker
      - DOCKER_SERVICE_NAMES=app1,app3
      - CLUSTER_NAME=docker-cluster
```

## 2. DNS-Based Discovery

```bash
# Set up DNS records for your NATS cluster
# nats-seed.example.com -> *********, *********, *********

# Start nodes with DNS discovery
DISCOVERY_METHOD=dns \
DISCOVERY_SEED_HOSTS=nats-seed.example.com \
CLUSTER_NAME=production-cluster \
./server
```

## 3. Kubernetes Discovery

```yaml
# k8s-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: nats-cluster
spec:
  replicas: 3
  selector:
    matchLabels:
      app: nats-cluster
  template:
    metadata:
      labels:
        app: nats-cluster
    spec:
      containers:
      - name: nats
        image: your-nats-app:latest
        env:
        - name: DISCOVERY_METHOD
          value: "kubernetes"
        - name: KUBERNETES_NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        - name: KUBERNETES_SERVICE_NAME
          value: "nats-cluster"
        - name: CLUSTER_NAME
          value: "k8s-cluster"
---
apiVersion: v1
kind: Service
metadata:
  name: nats-cluster
spec:
  selector:
    app: nats-cluster
  ports:
  - name: client
    port: 4222
    targetPort: 4222
  - name: cluster
    port: 6222
    targetPort: 6222
  clusterIP: None  # Headless service for discovery
```

## 4. Hybrid Approach (Seed + Auto-Discovery)

```bash
# Node 1 (seed node) - no routes needed
DISCOVERY_METHOD=auto \
CLUSTER_NAME=hybrid-cluster \
./server

# Node 2 - points to seed node, discovers others automatically
DISCOVERY_METHOD=auto \
NATS_ROUTES=nats://node1:6222 \
CLUSTER_NAME=hybrid-cluster \
./server

# Node 3 - also points to seed node
DISCOVERY_METHOD=auto \
NATS_ROUTES=nats://node1:6222 \
CLUSTER_NAME=hybrid-cluster \
./server
```

## 5. AWS/Cloud Discovery with DNS

```bash
# Use AWS Route 53 or similar for DNS-based discovery
# Create SRV records: _nats._tcp.cluster.example.com

DISCOVERY_METHOD=dns \
DISCOVERY_SEED_HOSTS=cluster.example.com \
CLUSTER_NAME=aws-cluster \
AWS_REGION=us-west-2 \
./server
```

## How It Works

1. **Auto Discovery Priority**:
   - Static routes (if `NATS_ROUTES` is set)
   - DNS seed hosts (if `DISCOVERY_SEED_HOSTS` is set)
   - Docker service discovery (tries common service names)
   - Kubernetes service discovery (if in K8s environment)

2. **Fallback Behavior**:
   - If no peers are discovered, runs as single node
   - Can join cluster later when peers become available
   - NATS gossip protocol handles dynamic membership

3. **Zero Configuration**:
   - In Docker Compose: Just set `DISCOVERY_METHOD=auto`
   - In Kubernetes: Use headless service + auto discovery
   - In cloud: Use DNS records for seed hosts

## Benefits

- **No Hardcoded IPs**: Uses service names, DNS, or auto-discovery
- **Environment Agnostic**: Works in Docker, K8s, cloud, or bare metal
- **Self-Healing**: Automatically handles node failures and recoveries
- **Scalable**: Easy to add/remove nodes without configuration changes
- **Production Ready**: Uses standard service discovery patterns
