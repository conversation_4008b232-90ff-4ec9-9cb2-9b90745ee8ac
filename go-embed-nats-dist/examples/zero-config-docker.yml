# Zero-Configuration Docker Compose Example
# No hardcoded service names - uses network scanning!

version: '3.8'

services:
  # Node 1 - <PERSON> scan network for other NATS nodes
  nats-node-1:
    build: .
    environment:
      - DISCOVERY_METHOD=auto
      - CLUSTER_NAME=zero-config-cluster
      - DEBUG=true
    networks:
      - nats-net
    ports:
      - "8080:8080"
      - "4222:4222"

  # Node 2 - Will find Node 1 via network scan
  nats-node-2:
    build: .
    environment:
      - DISCOVERY_METHOD=auto
      - CLUSTER_NAME=zero-config-cluster
      - DEBUG=true
    networks:
      - nats-net
    ports:
      - "8081:8080"
      - "4223:4222"

  # Node 3 - Will find Nodes 1 & 2 via network scan
  nats-node-3:
    build: .
    environment:
      - DISCOVERY_METHOD=auto
      - CLUSTER_NAME=zero-config-cluster
      - DEBUG=true
    networks:
      - nats-net
    ports:
      - "8082:8080"
      - "4224:4222"

  # You can add more nodes dynamically - they'll auto-discover!
  # nats-node-4:
  #   build: .
  #   environment:
  #     - DISCOVERY_METHOD=auto
  #     - CLUSTER_NAME=zero-config-cluster
  #     - DEBUG=true
  #   networks:
  #     - nats-net

networks:
  nats-net:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# Usage:
# 1. docker-compose -f examples/zero-config-docker.yml up
# 2. Watch logs - nodes will discover each other automatically
# 3. Test: curl http://localhost:8080/cluster
# 4. Add more nodes by uncommenting or scaling: docker-compose up --scale nats-node-1=5
