package discovery

import (
	"fmt"
	"log"
	"net"
	"net/url"
	"os"
	"strconv"
	"strings"
	"time"

	"go-embed-nats-dist/internal/config"
)

type DNSDiscovery struct {
	config      *config.Config
	serverID    string
	localIP     string
	seedHosts   []string
	clusterPort int
}

func NewDNSDiscovery(cfg *config.Config, serverID string) *DNSDiscovery {
	return &DNSDiscovery{
		config:      cfg,
		serverID:    serverID,
		localIP:     getLocalIP(),
		seedHosts:   cfg.Discovery.SeedHosts,
		clusterPort: cfg.NATS.ClusterPort,
	}
}

func (d *DNSDiscovery) DiscoverRoutes() ([]*url.URL, error) {
	var routes []*url.URL

	// If static routes are configured, use them
	if len(d.config.NATS.Routes) > 0 {
		for _, routeStr := range d.config.NATS.Routes {
			if routeURL, err := url.Parse(routeStr); err == nil {
				routes = append(routes, routeURL)
			} else {
				log.Printf("Warning: invalid route URL %s: %v", routeStr, err)
			}
		}
		return routes, nil
	}

	// Try DNS-based discovery
	if len(d.seedHosts) > 0 {
		return d.discoverFromSeedHosts()
	}

	// Try Docker Compose service discovery
	if dockerRoutes, err := d.discoverDockerServices(); err == nil && len(dockerRoutes) > 0 {
		return dockerRoutes, nil
	}

	// Try Kubernetes service discovery
	if k8sRoutes, err := d.discoverKubernetesServices(); err == nil && len(k8sRoutes) > 0 {
		return k8sRoutes, nil
	}

	log.Println("No routes discovered, running as single node")
	return routes, nil
}

func (d *DNSDiscovery) discoverFromSeedHosts() ([]*url.URL, error) {
	var routes []*url.URL

	for _, seedHost := range d.seedHosts {
		// Resolve the seed host
		ips, err := net.LookupIP(seedHost)
		if err != nil {
			log.Printf("Failed to resolve seed host %s: %v", seedHost, err)
			continue
		}

		for _, ip := range ips {
			// Skip our own IP
			if ip.String() == d.localIP {
				continue
			}

			routeURL := fmt.Sprintf("nats://%s:%d", ip.String(), d.clusterPort)
			if parsedURL, err := url.Parse(routeURL); err == nil {
				routes = append(routes, parsedURL)
				log.Printf("Discovered route via DNS: %s", routeURL)
			}
		}
	}

	return routes, nil
}

func (d *DNSDiscovery) discoverDockerServices() ([]*url.URL, error) {
	var routes []*url.URL

	// Get service names from environment or use network scanning
	serviceNames := d.getServiceNames()

	// If no service names configured, try network scanning
	if len(serviceNames) == 0 {
		return d.scanDockerNetwork()
	}

	for _, serviceName := range serviceNames {
		// Skip if this might be our own service
		if d.isOwnService(serviceName) {
			continue
		}

		// Try to resolve the service name
		ips, err := net.LookupIP(serviceName)
		if err != nil {
			continue // Service doesn't exist, skip
		}

		for _, ip := range ips {
			// Skip our own IP
			if ip.String() == d.localIP {
				continue
			}

			// Test if the cluster port is open
			if d.isPortOpen(ip.String(), d.clusterPort) {
				routeURL := fmt.Sprintf("nats://%s:%d", ip.String(), d.clusterPort)
				if parsedURL, err := url.Parse(routeURL); err == nil {
					routes = append(routes, parsedURL)
					log.Printf("Discovered Docker service: %s", routeURL)
				}
			}
		}
	}

	return routes, nil
}

func (d *DNSDiscovery) discoverKubernetesServices() ([]*url.URL, error) {
	var routes []*url.URL

	// Try to discover via Kubernetes DNS
	// Format: <service-name>.<namespace>.svc.cluster.local
	namespace := getEnv("KUBERNETES_NAMESPACE", "default")
	serviceName := getEnv("KUBERNETES_SERVICE_NAME", "nats")

	dnsName := fmt.Sprintf("%s.%s.svc.cluster.local", serviceName, namespace)

	ips, err := net.LookupIP(dnsName)
	if err != nil {
		return routes, err
	}

	for _, ip := range ips {
		// Skip our own IP
		if ip.String() == d.localIP {
			continue
		}

		routeURL := fmt.Sprintf("nats://%s:%d", ip.String(), d.clusterPort)
		if parsedURL, err := url.Parse(routeURL); err == nil {
			routes = append(routes, parsedURL)
			log.Printf("Discovered Kubernetes service: %s", routeURL)
		}
	}

	return routes, nil
}

func (d *DNSDiscovery) isPortOpen(host string, port int) bool {
	timeout := time.Second * 2
	conn, err := net.DialTimeout("tcp", net.JoinHostPort(host, strconv.Itoa(port)), timeout)
	if err != nil {
		return false
	}
	conn.Close()
	return true
}

func getLocalIP() string {
	conn, err := net.Dial("udp", "*******:80")
	if err != nil {
		return "127.0.0.1"
	}
	defer conn.Close()

	localAddr := conn.LocalAddr().(*net.UDPAddr)
	return localAddr.IP.String()
}

func (d *DNSDiscovery) getServiceNames() []string {
	// Try environment variable first
	if serviceNamesEnv := os.Getenv("DOCKER_SERVICE_NAMES"); serviceNamesEnv != "" {
		return strings.Split(serviceNamesEnv, ",")
	}

	// Try Docker Compose project name pattern
	if projectName := os.Getenv("COMPOSE_PROJECT_NAME"); projectName != "" {
		// Generate common service names based on project
		var services []string
		for i := 1; i <= 10; i++ { // Check up to 10 services
			services = append(services, fmt.Sprintf("%s_app%d_1", projectName, i))
			services = append(services, fmt.Sprintf("%s-app%d-1", projectName, i))
			services = append(services, fmt.Sprintf("app%d", i))
		}
		return services
	}

	// Return empty slice to trigger network scanning
	return []string{}
}

func (d *DNSDiscovery) isOwnService(serviceName string) bool {
	// Check if service name contains our server ID
	if strings.Contains(serviceName, d.serverID[:4]) {
		return true
	}

	// Check if it's our hostname
	if hostname, err := os.Hostname(); err == nil {
		if strings.Contains(serviceName, hostname) {
			return true
		}
	}

	return false
}

func (d *DNSDiscovery) scanDockerNetwork() ([]*url.URL, error) {
	var routes []*url.URL

	log.Println("No service names configured, scanning Docker network...")

	// Get our network interface to determine subnet
	interfaces, err := net.Interfaces()
	if err != nil {
		return routes, err
	}

	for _, iface := range interfaces {
		// Skip loopback and non-Docker interfaces
		if iface.Flags&net.FlagLoopback != 0 || !strings.Contains(iface.Name, "docker") {
			continue
		}

		addrs, err := iface.Addrs()
		if err != nil {
			continue
		}

		for _, addr := range addrs {
			if ipnet, ok := addr.(*net.IPNet); ok && !ipnet.IP.IsLoopback() {
				if ipnet.IP.To4() != nil {
					// Scan the subnet for NATS cluster ports
					routes = append(routes, d.scanSubnet(ipnet)...)
				}
			}
		}
	}

	return routes, nil
}

func (d *DNSDiscovery) scanSubnet(ipnet *net.IPNet) []*url.URL {
	var routes []*url.URL

	// Convert to 4-byte representation
	ip := ipnet.IP.To4()
	if ip == nil {
		return routes
	}

	// Scan common Docker network ranges (172.x.x.x, 192.168.x.x)
	baseIP := ip.Mask(ipnet.Mask)

	// Scan up to 254 IPs in the subnet (skip .0 and .255)
	for i := 1; i < 255; i++ {
		testIP := make(net.IP, 4)
		copy(testIP, baseIP)
		testIP[3] = byte(i)

		// Skip our own IP
		if testIP.String() == d.localIP {
			continue
		}

		// Test if NATS cluster port is open
		if d.isPortOpen(testIP.String(), d.clusterPort) {
			routeURL := fmt.Sprintf("nats://%s:%d", testIP.String(), d.clusterPort)
			if parsedURL, err := url.Parse(routeURL); err == nil {
				routes = append(routes, parsedURL)
				log.Printf("Discovered NATS node via network scan: %s", routeURL)
			}
		}
	}

	return routes
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
