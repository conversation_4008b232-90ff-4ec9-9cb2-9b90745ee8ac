package pubsub

import (
	"fmt"
	"log"
	"time"

	"go-embed-nats-dist/internal/config"

	natsgo "github.com/nats-io/nats.go"
)

type SimplePubSub struct {
	config *config.Config
	conn   *natsgo.Conn
}

func NewSimplePubSub(cfg *config.Config, natsURL string) *SimplePubSub {
	return &SimplePubSub{
		config: cfg,
	}
}

func (s *SimplePubSub) Start(natsURL string) error {
	var err error
	s.conn, err = natsgo.Connect(natsURL,
		natsgo.Name("simple-pubsub"),
		natsgo.MaxReconnects(-1),
		natsgo.ReconnectWait(time.Second),
	)
	if err != nil {
		return fmt.Errorf("failed to connect to NATS: %w", err)
	}

	// Subscribe to broadcast messages
	_, err = s.conn.Subscribe("broadcast.messages", s.handleBroadcastMessage)
	if err != nil {
		return fmt.Errorf("failed to subscribe to broadcast messages: %w", err)
	}

	log.Println("Simple PubSub started")
	return nil
}

func (s *SimplePubSub) Stop() error {
	if s.conn != nil {
		s.conn.Close()
	}
	return nil
}

func (s *SimplePubSub) PublishMessage(topic string, payload []byte) error {
	if s.conn == nil {
		return fmt.Errorf("not connected to NATS")
	}

	return s.conn.Publish(topic, payload)
}

func (s *SimplePubSub) handleBroadcastMessage(msg *natsgo.Msg) {
	log.Printf("Received broadcast message: %s", string(msg.Data))
}
