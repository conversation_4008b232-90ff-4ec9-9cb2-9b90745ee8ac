package pubsub

import (
	"context"
	"fmt"
	"log"
	"time"

	"go-embed-nats-dist/internal/config"

	"github.com/ThreeDotsLabs/watermill"
	"github.com/ThreeDotsLabs/watermill-nats/v2/pkg/nats"
	"github.com/ThreeDotsLabs/watermill/message"
	natsgo "github.com/nats-io/nats.go"
)

type WatermillManager struct {
	config     *config.Config
	natsURL    string
	publisher  message.Publisher
	subscriber message.Subscriber
	router     *message.Router
	logger     watermill.LoggerAdapter
}

func NewWatermillManager(cfg *config.Config, natsURL string) *WatermillManager {
	logger := watermill.NewStdLogger(cfg.App.Debug, cfg.App.Debug)

	return &WatermillManager{
		config:  cfg,
		natsURL: natsURL,
		logger:  logger,
	}
}

func (wm *WatermillManager) Start() error {
	// Create publisher
	if err := wm.createPublisher(); err != nil {
		return fmt.Errorf("failed to create publisher: %w", err)
	}

	// Create subscriber
	if err := wm.createSubscriber(); err != nil {
		return fmt.Errorf("failed to create subscriber: %w", err)
	}

	// Create and start router
	if err := wm.createRouter(); err != nil {
		return fmt.Errorf("failed to create router: %w", err)
	}

	log.Println("Watermill manager started")
	return nil
}

func (wm *WatermillManager) Stop() error {
	if wm.router != nil {
		if err := wm.router.Close(); err != nil {
			log.Printf("Error closing router: %v", err)
		}
	}

	if wm.publisher != nil {
		if err := wm.publisher.Close(); err != nil {
			log.Printf("Error closing publisher: %v", err)
		}
	}

	if wm.subscriber != nil {
		if err := wm.subscriber.Close(); err != nil {
			log.Printf("Error closing subscriber: %v", err)
		}
	}

	return nil
}

func (wm *WatermillManager) PublishMessage(topic string, payload []byte) error {
	msg := message.NewMessage(watermill.NewUUID(), payload)
	msg.Metadata.Set("timestamp", time.Now().Format(time.RFC3339))

	return wm.publisher.Publish(topic, msg)
}

func (wm *WatermillManager) GetPublisher() message.Publisher {
	return wm.publisher
}

func (wm *WatermillManager) GetSubscriber() message.Subscriber {
	return wm.subscriber
}

func (wm *WatermillManager) createPublisher() error {
	config := nats.PublisherConfig{
		Marshaler:         &nats.GobMarshaler{},
		SubjectCalculator: nats.DefaultSubjectCalculator,
		JetStream: nats.JetStreamConfig{
			AutoProvision: true,
		},
		NatsOptions: []natsgo.Option{
			natsgo.Name("watermill-publisher"),
			natsgo.MaxReconnects(-1),
			natsgo.ReconnectWait(time.Second),
			natsgo.ReconnectBufSize(8 * 1024 * 1024), // 8MB buffer
		},
	}

	var err error
	wm.publisher, err = nats.NewPublisher(config, wm.logger)
	if err != nil {
		return fmt.Errorf("failed to create NATS publisher: %w", err)
	}

	return nil
}

func (wm *WatermillManager) createSubscriber() error {
	config := nats.SubscriberConfig{
		Unmarshaler:       &nats.GobMarshaler{},
		SubjectCalculator: nats.DefaultSubjectCalculator,
		JetStream: nats.JetStreamConfig{
			AutoProvision: true,
			SubscribeOptions: []natsgo.SubOpt{
				natsgo.DeliverAll(),
				natsgo.AckExplicit(),
				natsgo.MaxDeliver(3),
				natsgo.AckWait(30 * time.Second),
			},
		},
		NatsOptions: []natsgo.Option{
			natsgo.Name("watermill-subscriber"),
			natsgo.MaxReconnects(-1),
			natsgo.ReconnectWait(time.Second),
		},
	}

	var err error
	wm.subscriber, err = nats.NewSubscriber(config, wm.logger)
	if err != nil {
		return fmt.Errorf("failed to create NATS subscriber: %w", err)
	}

	return nil
}

func (wm *WatermillManager) createRouter() error {
	wm.router, _ = message.NewRouter(message.RouterConfig{}, wm.logger)

	// Add message handler for test messages
	wm.router.AddHandler(
		"test_message_handler",
		"test.messages",
		wm.subscriber,
		"test.messages.processed",
		wm.publisher,
		wm.handleTestMessage,
	)

	// Add broadcast message handler
	wm.router.AddNoPublisherHandler(
		"broadcast_message_handler",
		"broadcast.messages",
		wm.subscriber,
		wm.handleBroadcastMessage,
	)

	// Start router in background
	go func() {
		if err := wm.router.Run(context.Background()); err != nil {
			log.Printf("Router error: %v", err)
		}
	}()

	// Wait for router to be ready
	<-wm.router.Running()

	return nil
}

func (wm *WatermillManager) handleTestMessage(msg *message.Message) ([]*message.Message, error) {
	log.Printf("Received test message: %s", string(msg.Payload))

	// Create response message
	response := message.NewMessage(watermill.NewUUID(), []byte(fmt.Sprintf("Processed: %s", string(msg.Payload))))
	response.Metadata.Set("original_id", msg.UUID)
	response.Metadata.Set("processed_at", time.Now().Format(time.RFC3339))

	return []*message.Message{response}, nil
}

func (wm *WatermillManager) handleBroadcastMessage(msg *message.Message) error {
	log.Printf("Received broadcast message: %s", string(msg.Payload))

	// Just log the message - this demonstrates that all nodes receive the broadcast
	timestamp := msg.Metadata.Get("timestamp")
	log.Printf("Broadcast message timestamp: %s", timestamp)

	return nil
}
