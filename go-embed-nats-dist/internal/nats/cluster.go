package nats

import (
	"log"
)

type ClusterManager struct {
	natsServer *EmbeddedNATS
}

func NewClusterManager(natsServer *EmbeddedNATS) *ClusterManager {
	return &ClusterManager{
		natsServer: natsServer,
	}
}

func (cm *ClusterManager) Start() error {
	log.Println("Cluster manager started - using NATS native clustering")
	return nil
}

func (cm *ClusterManager) Stop() {
	log.Println("Cluster manager stopped")
}

func (cm *ClusterManager) GetClusterInfo() map[string]interface{} {
	return map[string]interface{}{
		"server_id":   cm.natsServer.GetServerID(),
		"client_url":  cm.natsServer.GetClientURL(),
		"cluster_url": cm.natsServer.GetClusterURL(),
		"routes":      cm.natsServer.config.NATS.Routes,
	}
}
