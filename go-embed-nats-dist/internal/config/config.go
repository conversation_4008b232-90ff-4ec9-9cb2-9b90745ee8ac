package config

import (
	"os"
	"strconv"
	"strings"
	"time"
)

type Config struct {
	App       AppConfig       `json:"app"`
	NATS      NATSConfig      `json:"nats"`
	Discovery DiscoveryConfig `json:"discovery"`
	Watermill WatermillConfig `json:"watermill"`
	HTTP      HTTPConfig      `json:"http"`
}

type AppConfig struct {
	Name    string `json:"name"`
	Version string `json:"version"`
	Debug   bool   `json:"debug"`
}

type NATSConfig struct {
	ClientPort  int             `json:"client_port"`
	ClusterPort int             `json:"cluster_port"`
	ClusterName string          `json:"cluster_name"`
	Routes      []string        `json:"routes"`
	JetStream   JetStreamConfig `json:"jetstream"`
}

type JetStreamConfig struct {
	Enabled   bool   `json:"enabled"`
	MaxMemory int64  `json:"max_memory"`
	MaxFile   int64  `json:"max_file"`
	StoreDir  string `json:"store_dir"`
}

type DiscoveryConfig struct {
	Method    string   `json:"method"`
	SeedHosts []string `json:"seed_hosts"`
}

type WatermillConfig struct {
	Publisher  PublisherConfig  `json:"publisher"`
	Subscriber SubscriberConfig `json:"subscriber"`
}

type PublisherConfig struct {
	Async      bool `json:"async"`
	MaxPending int  `json:"max_pending"`
}

type SubscriberConfig struct {
	ConcurrentHandlers int           `json:"concurrent_handlers"`
	AckWait            time.Duration `json:"ack_wait"`
}

type HTTPConfig struct {
	Port int `json:"port"`
}

func Load() *Config {
	return &Config{
		App: AppConfig{
			Name:    getEnv("APP_NAME", "go-embed-nats-dist"),
			Version: getEnv("APP_VERSION", "1.0.0"),
			Debug:   getEnvBool("DEBUG", false),
		},
		NATS: NATSConfig{
			ClientPort:  getEnvInt("NATS_CLIENT_PORT", 4222),
			ClusterPort: getEnvInt("NATS_CLUSTER_PORT", 6222),
			ClusterName: getEnv("CLUSTER_NAME", "auto-cluster"),
			Routes:      getEnvStringSlice("NATS_ROUTES", []string{}),
			JetStream: JetStreamConfig{
				Enabled:   getEnvBool("JETSTREAM_ENABLED", true),
				MaxMemory: getEnvInt64("JETSTREAM_MAX_MEMORY", 1024*1024*1024),  // 1GB
				MaxFile:   getEnvInt64("JETSTREAM_MAX_FILE", 10*1024*1024*1024), // 10GB
				StoreDir:  getEnv("JETSTREAM_STORE_DIR", "/tmp/jetstream"),
			},
		},
		Discovery: DiscoveryConfig{
			Method:    getEnv("DISCOVERY_METHOD", "auto"),
			SeedHosts: getEnvStringSlice("DISCOVERY_SEED_HOSTS", []string{}),
		},

		Watermill: WatermillConfig{
			Publisher: PublisherConfig{
				Async:      getEnvBool("WATERMILL_ASYNC", true),
				MaxPending: getEnvInt("WATERMILL_MAX_PENDING", 1000),
			},
			Subscriber: SubscriberConfig{
				ConcurrentHandlers: getEnvInt("WATERMILL_CONCURRENT_HANDLERS", 10),
				AckWait:            getEnvDuration("WATERMILL_ACK_WAIT", 30*time.Second),
			},
		},
		HTTP: HTTPConfig{
			Port: getEnvInt("HTTP_PORT", 8080),
		},
	}
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func getEnvInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}

func getEnvInt64(key string, defaultValue int64) int64 {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.ParseInt(value, 10, 64); err == nil {
			return intValue
		}
	}
	return defaultValue
}

func getEnvBool(key string, defaultValue bool) bool {
	if value := os.Getenv(key); value != "" {
		if boolValue, err := strconv.ParseBool(value); err == nil {
			return boolValue
		}
	}
	return defaultValue
}

func getEnvDuration(key string, defaultValue time.Duration) time.Duration {
	if value := os.Getenv(key); value != "" {
		if duration, err := time.ParseDuration(value); err == nil {
			return duration
		}
	}
	return defaultValue
}

func getEnvStringSlice(key string, defaultValue []string) []string {
	if value := os.Getenv(key); value != "" {
		if value == "" {
			return []string{}
		}
		return strings.Split(value, ",")
	}
	return defaultValue
}
